import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';

class SettingsManager {
  static const String _appNameKey = 'app_name';
  static const String _appLogoKey = 'app_logo';
  static const String _isDarkModeKey = 'is_dark_mode';
  static const String _languageKey = 'language';
  static const String _currencyKey = 'currency';
  static const String _enableNotificationsKey = 'enable_notifications';
  static const String _enableBackupKey = 'enable_backup';
  static const String _lastBackupDateKey = 'last_backup_date';

  static SharedPreferences? _prefs;

  static Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  // App Name
  static Future<void> setAppName(String name) async {
    await init();
    await _prefs!.setString(_appNameKey, name);
  }

  static Future<String> getAppName() async {
    await init();
    return _prefs!.getString(_appNameKey) ?? 'إدارة الديون';
  }

  // App Logo
  static Future<void> setAppLogo(String? logoPath) async {
    await init();
    if (logoPath != null) {
      await _prefs!.setString(_appLogoKey, logoPath);
    } else {
      await _prefs!.remove(_appLogoKey);
    }
  }

  static Future<String?> getAppLogo() async {
    await init();
    return _prefs!.getString(_appLogoKey);
  }

  // Dark Mode
  static Future<void> setDarkMode(bool isDark) async {
    await init();
    await _prefs!.setBool(_isDarkModeKey, isDark);
  }

  static Future<bool> getDarkMode() async {
    await init();
    return _prefs!.getBool(_isDarkModeKey) ?? false;
  }

  // Language
  static Future<void> setLanguage(String language) async {
    await init();
    await _prefs!.setString(_languageKey, language);
  }

  static Future<String> getLanguage() async {
    await init();
    return _prefs!.getString(_languageKey) ?? 'ar';
  }

  // Currency
  static Future<void> setCurrency(String currency) async {
    await init();
    await _prefs!.setString(_currencyKey, currency);
  }

  static Future<String> getCurrency() async {
    await init();
    return _prefs!.getString(_currencyKey) ?? 'ر.س';
  }

  // Notifications
  static Future<void> setNotifications(bool enabled) async {
    await init();
    await _prefs!.setBool(_enableNotificationsKey, enabled);
  }

  static Future<bool> getNotifications() async {
    await init();
    return _prefs!.getBool(_enableNotificationsKey) ?? true;
  }

  // Backup
  static Future<void> setBackup(bool enabled) async {
    await init();
    await _prefs!.setBool(_enableBackupKey, enabled);
  }

  static Future<bool> getBackup() async {
    await init();
    return _prefs!.getBool(_enableBackupKey) ?? true;
  }

  // Last Backup Date
  static Future<void> setLastBackupDate(String date) async {
    await init();
    await _prefs!.setString(_lastBackupDateKey, date);
  }

  static Future<String?> getLastBackupDate() async {
    await init();
    return _prefs!.getString(_lastBackupDateKey);
  }

  // Clear all settings
  static Future<void> clearAll() async {
    await init();
    await _prefs!.clear();
  }
}

// Theme Provider for Dark Mode
class ThemeProvider extends ChangeNotifier {
  bool _isDarkMode = false;

  bool get isDarkMode => _isDarkMode;

  ThemeProvider() {
    _loadTheme();
  }

  void _loadTheme() async {
    _isDarkMode = await SettingsManager.getDarkMode();
    notifyListeners();
  }

  void toggleTheme() async {
    _isDarkMode = !_isDarkMode;
    await SettingsManager.setDarkMode(_isDarkMode);
    notifyListeners();
  }

  ThemeData get lightTheme => ThemeData(
    primarySwatch: Colors.blue,
    primaryColor: const Color(0xFF2C3E50),
    scaffoldBackgroundColor: const Color(0xFFF8F9FF),
    appBarTheme: const AppBarTheme(
      backgroundColor: Color(0xFF2C3E50),
      foregroundColor: Colors.white,
    ),
    fontFamily: 'LCB',
  );

  ThemeData get darkTheme => ThemeData(
    primarySwatch: Colors.blue,
    primaryColor: const Color(0xFF1A252F),
    scaffoldBackgroundColor: const Color(0xFF121212),
    appBarTheme: const AppBarTheme(
      backgroundColor: Color(0xFF1A252F),
      foregroundColor: Colors.white,
    ),
    cardColor: const Color(0xFF1E1E1E),
    textTheme: const TextTheme(
      bodyLarge: TextStyle(color: Colors.white),
      bodyMedium: TextStyle(color: Colors.white70),
    ),
    fontFamily: 'LCB',
  );
}

// App Name Provider
class AppNameProvider extends ChangeNotifier {
  String _appName = 'إدارة الديون';

  String get appName => _appName;

  AppNameProvider() {
    _loadAppName();
  }

  void _loadAppName() async {
    _appName = await SettingsManager.getAppName();
    notifyListeners();
  }

  void updateAppName(String newName) async {
    _appName = newName;
    await SettingsManager.setAppName(newName);
    notifyListeners();
  }
}

// App Logo Provider
class AppLogoProvider extends ChangeNotifier {
  String? _appLogo;

  String? get appLogo => _appLogo;

  AppLogoProvider() {
    _loadAppLogo();
  }

  void _loadAppLogo() async {
    _appLogo = await SettingsManager.getAppLogo();
    notifyListeners();
  }

  void updateAppLogo(String? newLogo) async {
    _appLogo = newLogo;
    await SettingsManager.setAppLogo(newLogo);
    notifyListeners();
  }
}

// Navigation Provider
class NavigationProvider extends ChangeNotifier {
  String _currentPage = 'home';

  String get currentPage => _currentPage;

  void setCurrentPage(String page) {
    _currentPage = page;
    notifyListeners();
  }

  bool isOnSettingsPage() {
    return _currentPage == 'settings';
  }

  void goToHome() {
    _currentPage = 'home';
    notifyListeners();
  }

  void goToSettings() {
    _currentPage = 'settings';
    notifyListeners();
  }
}
