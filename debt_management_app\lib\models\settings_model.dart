class AppSettings {
  final int? id;
  final String appName;
  final String? appLogo;
  final bool isDarkMode;
  final String language;
  final String currency;
  final bool enableNotifications;
  final bool enableBackup;
  final String? lastBackupDate;
  final DateTime updatedDate;

  AppSettings({
    this.id,
    this.appName = 'إدارة الديون',
    this.appLogo,
    this.isDarkMode = false,
    this.language = 'ar',
    this.currency = 'ر.س',
    this.enableNotifications = true,
    this.enableBackup = true,
    this.lastBackupDate,
    required this.updatedDate,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'app_name': appName,
      'app_logo': appLogo,
      'is_dark_mode': isDarkMode ? 1 : 0,
      'language': language,
      'currency': currency,
      'enable_notifications': enableNotifications ? 1 : 0,
      'enable_backup': enableBackup ? 1 : 0,
      'last_backup_date': lastBackupDate,
      'updated_date': updatedDate.millisecondsSinceEpoch,
    };
  }

  factory AppSettings.fromMap(Map<String, dynamic> map) {
    return AppSettings(
      id: map['id']?.toInt(),
      appName: map['app_name'] ?? 'إدارة الديون',
      appLogo: map['app_logo'],
      isDarkMode: map['is_dark_mode'] == 1,
      language: map['language'] ?? 'ar',
      currency: map['currency'] ?? 'ر.س',
      enableNotifications: map['enable_notifications'] == 1,
      enableBackup: map['enable_backup'] == 1,
      lastBackupDate: map['last_backup_date'],
      updatedDate: DateTime.fromMillisecondsSinceEpoch(map['updated_date']),
    );
  }

  AppSettings copyWith({
    int? id,
    String? appName,
    String? appLogo,
    bool? isDarkMode,
    String? language,
    String? currency,
    bool? enableNotifications,
    bool? enableBackup,
    String? lastBackupDate,
    DateTime? updatedDate,
  }) {
    return AppSettings(
      id: id ?? this.id,
      appName: appName ?? this.appName,
      appLogo: appLogo ?? this.appLogo,
      isDarkMode: isDarkMode ?? this.isDarkMode,
      language: language ?? this.language,
      currency: currency ?? this.currency,
      enableNotifications: enableNotifications ?? this.enableNotifications,
      enableBackup: enableBackup ?? this.enableBackup,
      lastBackupDate: lastBackupDate ?? this.lastBackupDate,
      updatedDate: updatedDate ?? this.updatedDate,
    );
  }
}
