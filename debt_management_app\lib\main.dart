import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:sqflite_common_ffi_web/sqflite_ffi_web.dart';
import 'package:sqflite/sqflite.dart';
import 'package:provider/provider.dart';
import 'screens/home_screen.dart';
import 'utils/settings_manager.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize database for web
  if (kIsWeb) {
    try {
      databaseFactory = databaseFactoryFfiWeb;
    } catch (e) {
      print('Database initialization error: $e');
    }
  }

  runApp(const DebtManagementApp());
}

class DebtManagementApp extends StatelessWidget {
  const DebtManagementApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AppNameProvider()),
        ChangeNotifierProvider(create: (_) => AppLogoProvider()),
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
        ChangeNotifierProvider(create: (_) => NavigationProvider()),
      ],
      child: Consumer3<AppNameProvider, AppLogoProvider, ThemeProvider>(
        builder: (
          context,
          appNameProvider,
          appLogoProvider,
          themeProvider,
          child,
        ) {
          return MaterialApp(
            title: appNameProvider.appName,
            debugShowCheckedModeBanner: false,
            theme: ThemeData(
              primarySwatch: Colors.blue,
              primaryColor: const Color(0xFF2C3E50),
              scaffoldBackgroundColor: const Color(0xFFF8F9FF),
              appBarTheme: const AppBarTheme(
                backgroundColor: Color(0xFF2C3E50),
                foregroundColor: Colors.white,
              ),
              fontFamily: 'LCB',
              useMaterial3: true,
            ),
            home: const HomeScreen(),
          );
        },
      ),
    );
  }
}
