import 'package:flutter/material.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  const CustomAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [const Color(0xFF2C3E50), const Color(0xFF34495E)],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            bool isSmallScreen = constraints.maxWidth < 800;
            bool isMediumScreen = constraints.maxWidth < 1200;

            return Padding(
              padding: EdgeInsets.symmetric(
                horizontal: isSmallScreen ? 8.0 : 16.0,
                vertical: 8.0,
              ),
              child: Row(
                children: [
                  // Logo and App Name (Left side)
                  Flexible(
                    flex: isSmallScreen ? 2 : 1,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: isSmallScreen ? 45 : 55,
                          height: isSmallScreen ? 45 : 55,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [Colors.white, Colors.grey.shade100],
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.15),
                                blurRadius: 8,
                                offset: const Offset(0, 3),
                              ),
                            ],
                          ),
                          child: Icon(
                            Icons.account_balance,
                            color: const Color(0xFF2C3E50),
                            size: isSmallScreen ? 24 : 30,
                          ),
                        ),
                        if (!isSmallScreen) ...[
                          const SizedBox(width: 16),
                          Text(
                            'إدارة الديون',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: isMediumScreen ? 18 : 22,
                              fontWeight: FontWeight.w600,
                              fontFamily: 'LCB',
                              letterSpacing: 1.2,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),

                  const Spacer(),

                  // Action Buttons (Right side)
                  Flexible(
                    flex: isSmallScreen ? 3 : 2,
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: _buildActionButtons(
                          isSmallScreen,
                          isMediumScreen,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  List<Widget> _buildActionButtons(bool isSmallScreen, bool isMediumScreen) {
    final buttons = [
      {
        'icon': Icons.notifications_outlined,
        'label': 'الإشعارات',
        'shortLabel': 'إشعارات',
      },
      {
        'icon': Icons.bar_chart,
        'label': 'الإحصائيات',
        'shortLabel': 'إحصائيات',
      },
      {'icon': Icons.assessment, 'label': 'التقارير', 'shortLabel': 'تقارير'},
      {'icon': Icons.payment, 'label': 'تسديد قسط', 'shortLabel': 'تسديد'},
      {
        'icon': Icons.add_circle_outline,
        'label': 'دين جديد',
        'shortLabel': 'دين جديد',
      },
      {'icon': Icons.people_outline, 'label': 'العملاء', 'shortLabel': 'عملاء'},
      {
        'icon': Icons.person_outline,
        'label': 'المستخدمين',
        'shortLabel': 'مستخدمين',
      },
      {
        'icon': Icons.settings_outlined,
        'label': 'الإعدادات',
        'shortLabel': 'إعدادات',
      },
    ];

    // Show fewer buttons on small screens, prioritize important ones
    List<Map<String, dynamic>> visibleButtons;
    if (isSmallScreen) {
      visibleButtons = [
        buttons[4], // دين جديد
        buttons[5], // العملاء
        buttons[6], // المستخدمين
        buttons[7], // الإعدادات
      ];
    } else if (isMediumScreen) {
      visibleButtons = buttons.take(6).toList();
    } else {
      visibleButtons = buttons;
    }

    List<Widget> widgets = [];
    for (int i = 0; i < visibleButtons.length; i++) {
      widgets.add(
        _buildActionButton(
          icon: visibleButtons[i]['icon'] as IconData,
          label: visibleButtons[i]['label'] as String,
          shortLabel: visibleButtons[i]['shortLabel'] as String,
          isSmallScreen: isSmallScreen,
          isMediumScreen: isMediumScreen,
          onTap: () {
            _handleButtonTap(visibleButtons[i]['label'] as String);
          },
        ),
      );
      if (i < visibleButtons.length - 1) {
        widgets.add(SizedBox(width: isSmallScreen ? 6 : 10));
      }
    }
    return widgets;
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    bool isSmallScreen = false,
  }) {
    return Tooltip(
      message: label,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: isSmallScreen ? 8 : 14,
            vertical: isSmallScreen ? 6 : 10,
          ),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.15),
            borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.25),
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(icon, color: Colors.white, size: isSmallScreen ? 16 : 18),
              if (!isSmallScreen) ...[
                const SizedBox(width: 8),
                Text(
                  label,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'LCB',
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(80);
}
