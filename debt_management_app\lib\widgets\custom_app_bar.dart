import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../screens/settings_screen.dart';
import '../utils/settings_manager.dart';

class CustomAppBar extends StatefulWidget implements PreferredSizeWidget {
  const CustomAppBar({super.key});

  @override
  State<CustomAppBar> createState() => _CustomAppBarState();

  @override
  Size get preferredSize => const Size.fromHeight(70);
}

class _CustomAppBarState extends State<CustomAppBar> {
  String? _activeButton;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [const Color(0xFF2C3E50), const Color(0xFF34495E)],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Safe<PERSON>rea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            bool isSmallScreen = constraints.maxWidth < 800;
            bool isMediumScreen = constraints.maxWidth < 1200;

            return Padding(
              padding: EdgeInsets.symmetric(
                horizontal: isSmallScreen ? 8.0 : 16.0,
                vertical: 8.0,
              ),
              child: Row(
                children: [
                  // Logo and App Name (Far Left)
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Consumer<AppLogoProvider>(
                        builder: (context, appLogoProvider, child) {
                          return Container(
                            width: isSmallScreen ? 42 : 48,
                            height: isSmallScreen ? 42 : 48,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [Colors.white, Colors.grey.shade50],
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.12),
                                  blurRadius: 6,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child:
                                appLogoProvider.appLogo != null
                                    ? ClipOval(
                                      child: Image.network(
                                        appLogoProvider.appLogo!,
                                        width: isSmallScreen ? 42 : 48,
                                        height: isSmallScreen ? 42 : 48,
                                        fit: BoxFit.cover,
                                        errorBuilder: (
                                          context,
                                          error,
                                          stackTrace,
                                        ) {
                                          return Icon(
                                            Icons.account_balance,
                                            color: const Color(0xFF2C3E50),
                                            size: isSmallScreen ? 20 : 24,
                                          );
                                        },
                                      ),
                                    )
                                    : Icon(
                                      Icons.account_balance,
                                      color: const Color(0xFF2C3E50),
                                      size: isSmallScreen ? 20 : 24,
                                    ),
                          );
                        },
                      ),
                      if (!isSmallScreen) ...[
                        const SizedBox(width: 12),
                        Consumer<AppNameProvider>(
                          builder: (context, appNameProvider, child) {
                            return Text(
                              appNameProvider.appName,
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: isMediumScreen ? 16 : 18,
                                fontWeight: FontWeight.w700,
                                fontFamily: 'LCB',
                                letterSpacing: 0.8,
                              ),
                            );
                          },
                        ),
                      ],
                    ],
                  ),

                  const Spacer(),

                  // Action Buttons (Far Right)
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: _buildActionButtons(
                      isSmallScreen,
                      isMediumScreen,
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  // Static button data for better performance
  static const List<Map<String, dynamic>> _buttonData = [
    {
      'icon': Icons.notifications_none,
      'label': 'الإشعارات',
      'shortLabel': '',
      'iconOnly': true,
    },
    {
      'icon': Icons.bar_chart_outlined,
      'label': 'الإحصائيات',
      'shortLabel': 'إحصائيات',
      'iconOnly': false,
    },
    {
      'icon': Icons.assessment_outlined,
      'label': 'التقارير',
      'shortLabel': 'تقارير',
      'iconOnly': false,
    },
    {
      'icon': Icons.payment_outlined,
      'label': 'تسديد قسط',
      'shortLabel': 'تسديد',
      'iconOnly': false,
    },
    {
      'icon': Icons.add_circle_outline,
      'label': 'دين جديد',
      'shortLabel': 'دين جديد',
      'iconOnly': false,
    },
    {
      'icon': Icons.people_outline,
      'label': 'العملاء',
      'shortLabel': 'عملاء',
      'iconOnly': false,
    },
    {
      'icon': Icons.person_outline,
      'label': 'المستخدمين',
      'shortLabel': 'مستخدمين',
      'iconOnly': false,
    },
    {
      'icon': Icons.settings_outlined,
      'label': 'الإعدادات',
      'shortLabel': 'إعدادات',
      'iconOnly': false,
    },
  ];

  List<Widget> _buildActionButtons(bool isSmallScreen, bool isMediumScreen) {
    // Determine visible buttons based on screen size
    final List<Map<String, dynamic>> visibleButtons;
    if (isSmallScreen) {
      // Show only essential buttons on small screens
      visibleButtons = [
        _buttonData[0], // الإشعارات
        _buttonData[4], // دين جديد
        _buttonData[5], // العملاء
        _buttonData[7], // الإعدادات
      ];
    } else if (isMediumScreen) {
      visibleButtons = _buttonData.take(6).toList();
    } else {
      visibleButtons = _buttonData;
    }

    final double spacing = isSmallScreen ? 4.0 : 6.0;

    return List.generate(visibleButtons.length * 2 - 1, (index) {
      if (index.isOdd) {
        return SizedBox(width: spacing);
      }

      final buttonIndex = index ~/ 2;
      final buttonData = visibleButtons[buttonIndex];

      return _buildActionButton(
        icon: buttonData['icon'] as IconData,
        label: buttonData['label'] as String,
        shortLabel: buttonData['shortLabel'] as String,
        iconOnly: buttonData['iconOnly'] as bool,
        isSmallScreen: isSmallScreen,
        isMediumScreen: isMediumScreen,
        isActive: _activeButton == buttonData['label'],
        onTap: () => _handleButtonTap(buttonData['label'] as String),
      );
    });
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required String shortLabel,
    required VoidCallback onTap,
    bool iconOnly = false,
    bool isSmallScreen = false,
    bool isMediumScreen = false,
    bool isActive = false,
  }) {
    // Choose appropriate label based on screen size and iconOnly flag
    String displayLabel =
        iconOnly
            ? ''
            : (isSmallScreen ? '' : (isMediumScreen ? shortLabel : label));

    // Special styling for notification bell
    bool isNotification = icon == Icons.notifications_none;

    return Tooltip(
      message: label,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(isNotification ? 20 : 8),
        child: Container(
          height: isSmallScreen ? 32 : 36,
          constraints: BoxConstraints(
            minWidth: isNotification ? 36 : (isSmallScreen ? 32 : 40),
            maxWidth:
                displayLabel.isEmpty
                    ? (isNotification ? 36 : (isSmallScreen ? 32 : 40))
                    : (isSmallScreen ? 80 : (isMediumScreen ? 100 : 120)),
          ),
          padding: EdgeInsets.symmetric(
            horizontal: displayLabel.isEmpty ? 0 : (isSmallScreen ? 8 : 10),
            vertical: 4,
          ),
          decoration:
              isNotification
                  ? BoxDecoration(
                    shape: BoxShape.circle,
                    color:
                        isActive
                            ? Colors.white.withValues(alpha: 0.3)
                            : Colors.white.withValues(alpha: 0.15),
                    border: Border.all(
                      color:
                          isActive
                              ? Colors.white.withValues(alpha: 0.5)
                              : Colors.white.withValues(alpha: 0.3),
                      width: isActive ? 2 : 1,
                    ),
                  )
                  : (displayLabel.isEmpty
                      ? null
                      : BoxDecoration(
                        color:
                            isActive
                                ? Colors.white.withValues(alpha: 0.15)
                                : Colors.transparent,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color:
                              isActive
                                  ? Colors.white.withValues(alpha: 0.4)
                                  : Colors.white.withValues(alpha: 0.2),
                          width: isActive ? 2 : 1,
                        ),
                      )),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: Colors.white,
                size: isNotification ? 18 : (isSmallScreen ? 16 : 18),
              ),
              if (displayLabel.isNotEmpty) ...[
                SizedBox(width: isSmallScreen ? 4 : 6),
                Flexible(
                  child: Text(
                    displayLabel,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: isSmallScreen ? 10 : (isMediumScreen ? 11 : 12),
                      fontWeight: FontWeight.w600,
                      fontFamily: 'LCB',
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  void _handleButtonTap(String buttonLabel) {
    setState(() {
      _activeButton = buttonLabel;
    });

    switch (buttonLabel) {
      case 'الإشعارات':
        _showComingSoon('الإشعارات');
        break;
      case 'الإحصائيات':
        _showComingSoon('الإحصائيات');
        break;
      case 'التقارير':
        _showComingSoon('التقارير');
        break;
      case 'تسديد قسط':
        _showComingSoon('تسديد قسط');
        break;
      case 'دين جديد':
        _showComingSoon('دين جديد');
        break;
      case 'العملاء':
        _showComingSoon('العملاء');
        break;
      case 'المستخدمين':
        _showComingSoon('المستخدمين');
        break;
      case 'الإعدادات':
        _handleSettingsButtonTap();
        break;
    }

    // Reset active button after a short delay
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        setState(() {
          _activeButton = null;
        });
      }
    });
  }

  void _handleSettingsButtonTap() {
    final navigationProvider = Provider.of<NavigationProvider>(
      context,
      listen: false,
    );

    if (navigationProvider.isOnSettingsPage()) {
      // إذا كنا في صفحة الإعدادات، ارجع للصفحة الرئيسية
      navigationProvider.goToHome();
      Navigator.pop(context);
    } else {
      // إذا كنا في الصفحة الرئيسية، اذهب لصفحة الإعدادات
      navigationProvider.goToSettings();
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const SettingsScreen()),
      );
    }
  }

  void _showComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'ميزة $feature قيد التطوير وستكون متاحة قريباً',
          style: const TextStyle(fontFamily: 'LCB'),
        ),
        backgroundColor: const Color(0xFF2C3E50),
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
