import 'package:flutter/material.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  const CustomAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [const Color(0xFF2C3E50), const Color(0xFF34495E)],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            bool isSmallScreen = constraints.maxWidth < 800;
            bool isMediumScreen = constraints.maxWidth < 1200;

            return Padding(
              padding: EdgeInsets.symmetric(
                horizontal: isSmallScreen ? 8.0 : 16.0,
                vertical: 8.0,
              ),
              child: Row(
                children: [
                  // Action Buttons (Far Left)
                  Expanded(
                    flex: isSmallScreen ? 4 : 3,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: _buildActionButtons(
                        isSmallScreen,
                        isMediumScreen,
                      ),
                    ),
                  ),

                  const Spacer(),

                  // Logo and App Name (Far Right)
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (!isSmallScreen) ...[
                        Text(
                          'إدارة الديون',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: isMediumScreen ? 18 : 22,
                            fontWeight: FontWeight.w600,
                            fontFamily: 'LCB',
                            letterSpacing: 1.2,
                          ),
                        ),
                        const SizedBox(width: 16),
                      ],
                      Container(
                        width: isSmallScreen ? 45 : 55,
                        height: isSmallScreen ? 45 : 55,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [Colors.white, Colors.grey.shade100],
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.15),
                              blurRadius: 8,
                              offset: const Offset(0, 3),
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.account_balance,
                          color: const Color(0xFF2C3E50),
                          size: isSmallScreen ? 24 : 30,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  // Static button data for better performance
  static const List<Map<String, dynamic>> _buttonData = [
    {
      'icon': Icons.notifications_outlined,
      'label': 'الإشعارات',
      'shortLabel': 'إشعارات',
    },
    {'icon': Icons.bar_chart, 'label': 'الإحصائيات', 'shortLabel': 'إحصائيات'},
    {'icon': Icons.assessment, 'label': 'التقارير', 'shortLabel': 'تقارير'},
    {'icon': Icons.payment, 'label': 'تسديد قسط', 'shortLabel': 'تسديد'},
    {
      'icon': Icons.add_circle_outline,
      'label': 'دين جديد',
      'shortLabel': 'دين جديد',
    },
    {'icon': Icons.people_outline, 'label': 'العملاء', 'shortLabel': 'عملاء'},
    {
      'icon': Icons.person_outline,
      'label': 'المستخدمين',
      'shortLabel': 'مستخدمين',
    },
    {
      'icon': Icons.settings_outlined,
      'label': 'الإعدادات',
      'shortLabel': 'إعدادات',
    },
  ];

  List<Widget> _buildActionButtons(bool isSmallScreen, bool isMediumScreen) {
    // Determine visible buttons based on screen size
    final List<Map<String, dynamic>> visibleButtons;
    if (isSmallScreen) {
      // Show only essential buttons on small screens
      visibleButtons = [
        _buttonData[4],
        _buttonData[5],
        _buttonData[6],
        _buttonData[7],
      ];
    } else if (isMediumScreen) {
      visibleButtons = _buttonData.take(6).toList();
    } else {
      visibleButtons = _buttonData;
    }

    final double spacing = isSmallScreen ? 8.0 : 12.0;

    return List.generate(visibleButtons.length * 2 - 1, (index) {
      if (index.isOdd) {
        return SizedBox(width: spacing);
      }

      final buttonIndex = index ~/ 2;
      final buttonData = visibleButtons[buttonIndex];

      return _buildActionButton(
        icon: buttonData['icon'] as IconData,
        label: buttonData['label'] as String,
        shortLabel: buttonData['shortLabel'] as String,
        isSmallScreen: isSmallScreen,
        isMediumScreen: isMediumScreen,
        onTap: () => _handleButtonTap(buttonData['label'] as String),
      );
    });
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required String shortLabel,
    required VoidCallback onTap,
    bool isSmallScreen = false,
    bool isMediumScreen = false,
  }) {
    // Choose appropriate label based on screen size
    String displayLabel =
        isSmallScreen ? '' : (isMediumScreen ? shortLabel : label);

    return Tooltip(
      message: label,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
        child: Container(
          height: isSmallScreen ? 45 : 50, // Fixed height to prevent overflow
          constraints: BoxConstraints(
            minWidth: isSmallScreen ? 45 : 70,
            maxWidth: isSmallScreen ? 60 : (isMediumScreen ? 120 : 160),
          ),
          padding: EdgeInsets.symmetric(
            horizontal: isSmallScreen ? 10 : (isMediumScreen ? 12 : 14),
            vertical: isSmallScreen ? 8 : 10,
          ),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.35),
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: Colors.white, size: isSmallScreen ? 20 : 22),
              if (displayLabel.isNotEmpty) ...[
                SizedBox(width: isSmallScreen ? 6 : 10),
                Expanded(
                  child: Text(
                    displayLabel,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: isSmallScreen ? 12 : (isMediumScreen ? 13 : 14),
                      fontWeight: FontWeight.w700,
                      fontFamily: 'LCB',
                    ),
                    overflow: TextOverflow.visible,
                    maxLines: 1,
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  void _handleButtonTap(String buttonLabel) {
    // Navigation will be implemented here
    // For now, just handle the tap without logging
    switch (buttonLabel) {
      case 'الإشعارات':
        // Navigate to notifications
        break;
      case 'الإحصائيات':
        // Navigate to statistics
        break;
      case 'التقارير':
        // Navigate to reports
        break;
      case 'تسديد قسط':
        // Navigate to payment
        break;
      case 'دين جديد':
        // Navigate to add debt
        break;
      case 'العملاء':
        // Navigate to clients
        break;
      case 'المستخدمين':
        // Navigate to users
        break;
      case 'الإعدادات':
        // Navigate to settings
        break;
    }
  }

  @override
  Size get preferredSize => const Size.fromHeight(85);
}
