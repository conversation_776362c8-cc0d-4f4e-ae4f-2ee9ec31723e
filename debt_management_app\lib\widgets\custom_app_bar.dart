import 'package:flutter/material.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  const CustomAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [const Color(0xFF2C3E50), const Color(0xFF34495E)],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            bool isSmallScreen = constraints.maxWidth < 800;
            bool isMediumScreen = constraints.maxWidth < 1200;

            return Padding(
              padding: EdgeInsets.symmetric(
                horizontal: isSmallScreen ? 8.0 : 16.0,
                vertical: 8.0,
              ),
              child: Row(
                children: [
                  // Action Buttons (Left side - moved to far left)
                  Expanded(
                    flex: isSmallScreen ? 4 : 3,
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: _buildActionButtons(
                          isSmallScreen,
                          isMediumScreen,
                        ),
                      ),
                    ),
                  ),

                  const Spacer(),

                  // Logo and App Name (Right side)
                  Flexible(
                    flex: isSmallScreen ? 2 : 1,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        if (!isSmallScreen) ...[
                          Text(
                            'إدارة الديون',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: isMediumScreen ? 18 : 22,
                              fontWeight: FontWeight.w600,
                              fontFamily: 'LCB',
                              letterSpacing: 1.2,
                            ),
                          ),
                          const SizedBox(width: 16),
                        ],
                        Container(
                          width: isSmallScreen ? 45 : 55,
                          height: isSmallScreen ? 45 : 55,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [Colors.white, Colors.grey.shade100],
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.15),
                                blurRadius: 8,
                                offset: const Offset(0, 3),
                              ),
                            ],
                          ),
                          child: Icon(
                            Icons.account_balance,
                            color: const Color(0xFF2C3E50),
                            size: isSmallScreen ? 24 : 30,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  List<Widget> _buildActionButtons(bool isSmallScreen, bool isMediumScreen) {
    final buttons = [
      {
        'icon': Icons.notifications_outlined,
        'label': 'الإشعارات',
        'shortLabel': 'إشعارات',
      },
      {
        'icon': Icons.bar_chart,
        'label': 'الإحصائيات',
        'shortLabel': 'إحصائيات',
      },
      {'icon': Icons.assessment, 'label': 'التقارير', 'shortLabel': 'تقارير'},
      {'icon': Icons.payment, 'label': 'تسديد قسط', 'shortLabel': 'تسديد'},
      {
        'icon': Icons.add_circle_outline,
        'label': 'دين جديد',
        'shortLabel': 'دين جديد',
      },
      {'icon': Icons.people_outline, 'label': 'العملاء', 'shortLabel': 'عملاء'},
      {
        'icon': Icons.person_outline,
        'label': 'المستخدمين',
        'shortLabel': 'مستخدمين',
      },
      {
        'icon': Icons.settings_outlined,
        'label': 'الإعدادات',
        'shortLabel': 'إعدادات',
      },
    ];

    // Show fewer buttons on small screens, prioritize important ones
    List<Map<String, dynamic>> visibleButtons;
    if (isSmallScreen) {
      visibleButtons = [
        buttons[4], // دين جديد
        buttons[5], // العملاء
        buttons[6], // المستخدمين
        buttons[7], // الإعدادات
      ];
    } else if (isMediumScreen) {
      visibleButtons = buttons.take(6).toList();
    } else {
      visibleButtons = buttons;
    }

    List<Widget> widgets = [];
    for (int i = 0; i < visibleButtons.length; i++) {
      widgets.add(
        _buildActionButton(
          icon: visibleButtons[i]['icon'] as IconData,
          label: visibleButtons[i]['label'] as String,
          shortLabel: visibleButtons[i]['shortLabel'] as String,
          isSmallScreen: isSmallScreen,
          isMediumScreen: isMediumScreen,
          onTap: () {
            _handleButtonTap(visibleButtons[i]['label'] as String);
          },
        ),
      );
      if (i < visibleButtons.length - 1) {
        widgets.add(SizedBox(width: isSmallScreen ? 8 : 12));
      }
    }
    return widgets;
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required String shortLabel,
    required VoidCallback onTap,
    bool isSmallScreen = false,
    bool isMediumScreen = false,
  }) {
    // Choose appropriate label based on screen size
    String displayLabel =
        isSmallScreen ? '' : (isMediumScreen ? shortLabel : label);

    return Tooltip(
      message: label,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
        child: Container(
          height: isSmallScreen ? 45 : 50, // Fixed height to prevent overflow
          constraints: BoxConstraints(
            minWidth: isSmallScreen ? 45 : 70,
            maxWidth: isSmallScreen ? 60 : (isMediumScreen ? 120 : 160),
          ),
          padding: EdgeInsets.symmetric(
            horizontal: isSmallScreen ? 10 : (isMediumScreen ? 12 : 14),
            vertical: isSmallScreen ? 8 : 10,
          ),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.35),
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: Colors.white, size: isSmallScreen ? 20 : 22),
              if (displayLabel.isNotEmpty) ...[
                SizedBox(width: isSmallScreen ? 6 : 10),
                Expanded(
                  child: Text(
                    displayLabel,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: isSmallScreen ? 12 : (isMediumScreen ? 13 : 14),
                      fontWeight: FontWeight.w700,
                      fontFamily: 'LCB',
                    ),
                    overflow: TextOverflow.visible,
                    maxLines: 1,
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  void _handleButtonTap(String buttonLabel) {
    // TODO: Implement navigation based on button label
    print('تم الضغط على: $buttonLabel');
    // يمكن إضافة التنقل هنا لاحقاً
  }

  @override
  Size get preferredSize => const Size.fromHeight(85);
}
