{"accessibility": {"captions": {"live_caption_language": "en-US"}}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "", "shortcuts_version": 1}, "autocomplete": {"retention_policy_last_version": 136}, "autofill": {"last_version_deduped": 136}, "browser": {"has_seen_welcome_page": false, "window_placement": {"bottom": 807, "left": 9, "maximized": false, "right": 1257, "top": 9, "work_area_bottom": 816, "work_area_left": 0, "work_area_right": 1536, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 21843, "default_search_provider": {"guid": ""}, "devtools": {"last_open_timestamp": "**************", "preferences": {"closeable-tabs": "{\"security\":true,\"freestyler\":true,\"chrome-recorder\":true}", "console.sidebar-selected-filter": "\"message\"", "console.sidebar.width": "{\"vertical\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "currentDockState": "\"right\"", "elements.styles.sidebar.width": "{\"vertical\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "inspector-view.split-view-state": "{\"vertical\":{\"size\":0}}", "inspector.drawer-split-view-state": "{\"horizontal\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "inspectorVersion": "38", "network-panel-sidebar-state": "{\"vertical\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "network-panel-split-view-state": "{\"vertical\":{\"size\":0}}", "network-panel-split-view-waterfall": "{\"vertical\":{\"size\":0}}", "panel-selected-tab": "\"elements\"", "panel-tab-order": "{\"console\":10,\"elements\":20,\"sources\":30,\"network\":40,\"timeline\":50,\"heap-profiler\":60,\"resources\":70,\"security\":80,\"lighthouse\":90,\"chrome-recorder\":100}", "releaseNoteVersionSeen": "77", "sources-panel-navigator-split-view-state": "{\"vertical\":{\"size\":0,\"showMode\":\"Both\"}}", "sources-panel-split-view-state": "{\"vertical\":{\"size\":0,\"showMode\":\"Both\"},\"horizontal\":{\"size\":0,\"showMode\":\"Both\"}}", "styles-pane-sidebar-selected-tab": "\"elements.event-listeners\"", "styles-pane-sidebar-tab-order": "{\"styles\":10,\"computed\":20}", "undefined-tab-order": "{\"sources.scope-chain\":10,\"sources.watch\":20}"}, "synced_preferences_sync_disabled": {"adorner-settings": "[{\"adorner\":\"grid\",\"isEnabled\":true},{\"adorner\":\"subgrid\",\"isEnabled\":true},{\"adorner\":\"flex\",\"isEnabled\":true},{\"adorner\":\"ad\",\"isEnabled\":true},{\"adorner\":\"scroll-snap\",\"isEnabled\":true},{\"adorner\":\"container\",\"isEnabled\":true},{\"adorner\":\"slot\",\"isEnabled\":true},{\"adorner\":\"top-layer\",\"isEnabled\":true},{\"adorner\":\"reveal\",\"isEnabled\":true},{\"adorner\":\"media\",\"isEnabled\":false},{\"adorner\":\"scroll\",\"isEnabled\":true}]", "syncedInspectorVersion": "38"}}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "0716aa85-81e5-4d7e-8e98-ec3176c760c3", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "136.0.7103.114"}, "gaia_cookie": {"changed_time": **********.420424, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_data": "[\"gaia.l.a.r\",[]]"}, "gcm": {"product_category_for_subtypes": "com.chrome.windows"}, "google": {"services": {"signin_scoped_device_id": "e051cd2b-a1a3-4618-99bb-ae27b2fc3a8f"}}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "LensOverlay": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "*****************", "recent_session_start_times": ["*****************"], "session_last_active_time": "*****************", "session_start_time": "*****************"}, "intl": {"selected_languages": "en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"*************": {}}}, "media": {"device_id_salt": "E8A5F44F8C428624315398DC584B9AB4", "engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "aDAQBS4pdrskB5a8G3GupfVJVTgCljRPtvtKZtkJ6GIo0A78C0mGvGVyMY0WfBQ8gp8dZ/oJ9PqfdhtFNTGnNw=="}, "net": {"network_prediction_options": 0}, "ntp": {"num_personal_suggestions": 2}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "predictionmodelfetcher": {"last_fetch_attempt": "*****************", "last_fetch_success": "*****************"}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PAGE_ENTITIES": true, "PRICE_INSIGHTS": true, "PRICE_TRACKING": true, "SALIENT_IMAGE": true, "SHOPPING_DISCOUNTS": true, "SHOPPING_PAGE_TYPES": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false}, "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "background_password_check": {"check_fri_weight": 9, "check_interval": "*************", "check_mon_weight": 8, "check_sat_weight": 6, "check_sun_weight": 6, "check_thu_weight": 9, "check_tue_weight": 9, "check_wed_weight": 9, "next_check_time": "*****************"}, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"http://localhost,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"http://localhost:8081,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"chrome://settings/,*": {"last_modified": "13392761542024333", "setting": {"lastEngagementTime": 1.3392761542024296e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.6, "rawScore": 3.6}}, "http://localhost:8081,*": {"last_modified": "13392761647818088", "setting": {"lastEngagementTime": 1.3392761647818038e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 8.399999999999999, "rawScore": 8.399999999999999}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "136.0.7103.114", "creation_time": "13392761234361259", "did_work_around_bug_364820109_default": true, "did_work_around_bug_364820109_exceptions": true, "exit_type": "Normal", "family_member_role": "not_in_family", "last_engagement_time": "13392761647818038", "last_time_obsolete_http_credentials_removed": 1748287694.405299, "last_time_password_store_metrics_reported": 1748287664.402775, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "Your Chrome", "password_hash_data_list": [], "were_old_google_logins_removed": true}, "safebrowsing": {"event_timestamps": {}, "hash_real_time_ohttp_expiration_time": "13393020435783921", "hash_real_time_ohttp_key": "0gAgz7NnQTpxaNrkE/W7d+fc3Njc9M9rqIL5N2yollHsMw4ABAABAAI=", "metrics_last_log_time": "13392761234", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "segmentation_platform": {"client_result_prefs": "ClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQ3paEwtOU5RcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADEKaXhMLTlOUX", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13392604799000000", "uma_in_sql_start_time": "13392761234400378"}, "sessions": {"event_log": [{"crashed": false, "time": "13392761234397205", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13392761860794907", "type": 2, "window_count": 1}], "session_data_status": 3}, "settings": {"force_google_safesearch": false}, "signin": {"allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["en-US"], "dictionary": ""}, "sync": {"data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_valuable": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": false, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": false, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": false, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": false, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": false, "send_tab_to_self": false, "sessions": false, "shared_tab_group_account_data": false, "shared_tab_group_data": false, "sharing_message": false, "themes": false, "user_consent": false, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account_migration_done": true, "feature_status_for_sync_to_signin": 5, "passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "toolbar": {"pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "136"}}