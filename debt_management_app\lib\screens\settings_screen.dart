import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:convert';
import '../models/settings_model.dart';
import '../utils/settings_manager.dart';
import '../utils/backup_manager.dart';
import '../widgets/custom_app_bar.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final TextEditingController _appNameController = TextEditingController();

  AppSettings? _currentSettings;
  bool _isLoading = true;
  String? _selectedImagePath;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final appName = await SettingsManager.getAppName();
      final currency = await SettingsManager.getCurrency();
      final isDarkMode = await SettingsManager.getDarkMode();
      final enableNotifications = await SettingsManager.getNotifications();
      final enableBackup = await SettingsManager.getBackup();
      final appLogo = await SettingsManager.getAppLogo();

      final settings = AppSettings(
        appName: appName,
        currency: currency,
        isDarkMode: isDarkMode,
        enableNotifications: enableNotifications,
        enableBackup: enableBackup,
        appLogo: appLogo,
        updatedDate: DateTime.now(),
      );

      setState(() {
        _currentSettings = settings;
        _appNameController.text = settings.appName;
        _selectedImagePath = settings.appLogo;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('خطأ في تحميل الإعدادات: $e');
    }
  }

  Future<void> _updateSettings(AppSettings newSettings) async {
    try {
      // Save to SharedPreferences
      await SettingsManager.setAppName(newSettings.appName);
      await SettingsManager.setCurrency(newSettings.currency);
      await SettingsManager.setDarkMode(newSettings.isDarkMode);
      await SettingsManager.setNotifications(newSettings.enableNotifications);
      await SettingsManager.setBackup(newSettings.enableBackup);
      if (newSettings.appLogo != null) {
        await SettingsManager.setAppLogo(newSettings.appLogo);
      }

      setState(() {
        _currentSettings = newSettings;
      });

      // Update providers immediately
      if (mounted) {
        final appNameProvider = Provider.of<AppNameProvider>(
          context,
          listen: false,
        );
        final themeProvider = Provider.of<ThemeProvider>(
          context,
          listen: false,
        );

        // Update app name immediately
        if (appNameProvider.appName != newSettings.appName) {
          appNameProvider.updateAppName(newSettings.appName);
        }

        // Update theme immediately
        if (themeProvider.isDarkMode != newSettings.isDarkMode) {
          themeProvider.toggleTheme();
        }
      }

      _showSuccessSnackBar('تم حفظ الإعدادات بنجاح');
    } catch (e) {
      _showErrorSnackBar('خطأ في حفظ الإعدادات: $e');
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FF),
      appBar: const CustomAppBar(),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Padding(
                padding: const EdgeInsets.all(20.0),
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 15,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildPageHeader(),
                        const SizedBox(height: 30),
                        _buildGeneralSettings(),
                        const SizedBox(height: 25),
                        _buildAppearanceSettings(),
                        const SizedBox(height: 25),
                        _buildBackupSettings(),
                        const SizedBox(height: 25),
                        _buildAboutSettings(),
                      ],
                    ),
                  ),
                ),
              ),
    );
  }

  Widget _buildPageHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [const Color(0xFF2C3E50), const Color(0xFF34495E)],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF2C3E50).withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Icon(Icons.settings, color: Colors.white, size: 32),
          ),
          const SizedBox(width: 20),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'إعدادات البرنامج',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'LCB',
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'تخصيص وإدارة إعدادات التطبيق',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.8),
                    fontSize: 14,
                    fontFamily: 'LCB',
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGeneralSettings() {
    return _buildSettingsSection(
      title: 'الإعدادات العامة',
      icon: Icons.tune,
      children: [
        _buildTextFieldSetting(
          title: 'اسم التطبيق',
          controller: _appNameController,
          icon: Icons.edit,
          onChanged: (value) {
            if (_currentSettings != null && value.isNotEmpty) {
              // Update immediately in UI
              final appNameProvider = Provider.of<AppNameProvider>(
                context,
                listen: false,
              );
              appNameProvider.updateAppName(value);

              // Save to storage
              _updateSettings(
                _currentSettings!.copyWith(
                  appName: value,
                  updatedDate: DateTime.now(),
                ),
              );
            }
          },
        ),
        const SizedBox(height: 16),
        _buildImagePickerSetting(),
        const SizedBox(height: 16),
        _buildSwitchSetting(
          title: 'تفعيل الإشعارات',
          subtitle: 'استقبال إشعارات التطبيق',
          icon: Icons.notifications,
          value: _currentSettings?.enableNotifications ?? true,
          onChanged: (value) {
            if (_currentSettings != null) {
              _updateSettings(
                _currentSettings!.copyWith(
                  enableNotifications: value,
                  updatedDate: DateTime.now(),
                ),
              );
            }
          },
        ),
      ],
    );
  }

  Widget _buildAppearanceSettings() {
    return _buildSettingsSection(
      title: 'المظهر والعرض',
      icon: Icons.palette,
      children: [
        _buildSwitchSetting(
          title: 'الوضع الليلي',
          subtitle: 'تفعيل المظهر الداكن',
          icon: Icons.dark_mode,
          value: _currentSettings?.isDarkMode ?? false,
          onChanged: (value) {
            if (_currentSettings != null) {
              // Update immediately in UI
              final themeProvider = Provider.of<ThemeProvider>(
                context,
                listen: false,
              );
              if (themeProvider.isDarkMode != value) {
                themeProvider.toggleTheme();
              }

              // Save to storage
              _updateSettings(
                _currentSettings!.copyWith(
                  isDarkMode: value,
                  updatedDate: DateTime.now(),
                ),
              );
            }
          },
        ),
        const SizedBox(height: 16),
        _buildDropdownSetting(
          title: 'العملة',
          icon: Icons.monetization_on,
          value: _currentSettings?.currency ?? 'ر.س',
          items: ['ر.س', 'د.ك', 'د.إ', 'ج.م', 'د.ل'],
          onChanged: (value) {
            if (_currentSettings != null && value != null) {
              _updateSettings(
                _currentSettings!.copyWith(
                  currency: value,
                  updatedDate: DateTime.now(),
                ),
              );
            }
          },
        ),
      ],
    );
  }

  Widget _buildBackupSettings() {
    return _buildSettingsSection(
      title: 'النسخ الاحتياطي',
      icon: Icons.backup,
      children: [
        _buildActionSetting(
          title: 'إنشاء نسخة احتياطية',
          subtitle: 'حفظ بيانات التطبيق',
          icon: Icons.cloud_upload,
          onTap: _createBackup,
        ),
        const SizedBox(height: 16),
        _buildActionSetting(
          title: 'استعادة نسخة احتياطية',
          subtitle: 'استرداد البيانات المحفوظة',
          icon: Icons.cloud_download,
          onTap: _restoreBackup,
        ),
        const SizedBox(height: 16),
        _buildSwitchSetting(
          title: 'النسخ التلقائي',
          subtitle: 'إنشاء نسخ احتياطية تلقائياً',
          icon: Icons.schedule,
          value: _currentSettings?.enableBackup ?? true,
          onChanged: (value) {
            if (_currentSettings != null) {
              _updateSettings(
                _currentSettings!.copyWith(
                  enableBackup: value,
                  updatedDate: DateTime.now(),
                ),
              );
            }
          },
        ),
      ],
    );
  }

  Widget _buildAboutSettings() {
    return _buildSettingsSection(
      title: 'حول التطبيق',
      icon: Icons.info,
      children: [
        _buildActionSetting(
          title: 'الدعم الفني',
          subtitle: 'التواصل مع فريق الدعم',
          icon: Icons.support_agent,
          onTap: _showSupport,
        ),
        const SizedBox(height: 16),
        _buildActionSetting(
          title: 'حول البرنامج',
          subtitle: 'معلومات الإصدار والمطور',
          icon: Icons.info_outline,
          onTap: _showAbout,
        ),
        const SizedBox(height: 16),
        _buildActionSetting(
          title: 'تقييم التطبيق',
          subtitle: 'شاركنا رأيك في التطبيق',
          icon: Icons.star_rate,
          onTap: _rateApp,
        ),
      ],
    );
  }

  Widget _buildSettingsSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF2C3E50).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: const Color(0xFF2C3E50), size: 20),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2C3E50),
                  fontFamily: 'LCB',
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSwitchSetting({
    required String title,
    required String subtitle,
    required IconData icon,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: const Color(0xFF2C3E50).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: const Color(0xFF2C3E50), size: 20),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF2C3E50),
                  fontFamily: 'LCB',
                ),
              ),
              Text(
                subtitle,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                  fontFamily: 'LCB',
                ),
              ),
            ],
          ),
        ),
        Switch(
          value: value,
          onChanged: onChanged,
          activeColor: const Color(0xFF2C3E50),
        ),
      ],
    );
  }

  Widget _buildTextFieldSetting({
    required String title,
    required TextEditingController controller,
    required IconData icon,
    required Function(String) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Color(0xFF2C3E50),
            fontFamily: 'LCB',
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          onChanged: onChanged,
          decoration: InputDecoration(
            prefixIcon: Icon(icon, color: const Color(0xFF2C3E50)),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFF2C3E50)),
            ),
            filled: true,
            fillColor: Colors.grey.shade50,
          ),
        ),
      ],
    );
  }

  Widget _buildImagePickerSetting() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'شعار التطبيق',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Color(0xFF2C3E50),
            fontFamily: 'LCB',
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: _pickImage,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            height: 60,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(12),
              color: Colors.grey.shade50,
            ),
            child: Row(
              children: [
                Icon(Icons.image, color: const Color(0xFF2C3E50)),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _selectedImagePath != null
                        ? 'تم اختيار الصورة'
                        : 'اختر صورة من الجهاز',
                    style: TextStyle(
                      color:
                          _selectedImagePath != null
                              ? const Color(0xFF2C3E50)
                              : Colors.grey.shade600,
                      fontFamily: 'LCB',
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: Colors.grey.shade600,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDropdownSetting({
    required String title,
    required IconData icon,
    required String value,
    required List<String> items,
    required Function(String?) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Color(0xFF2C3E50),
            fontFamily: 'LCB',
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: value,
          onChanged: onChanged,
          decoration: InputDecoration(
            prefixIcon: Icon(icon, color: const Color(0xFF2C3E50)),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFF2C3E50)),
            ),
            filled: true,
            fillColor: Colors.grey.shade50,
          ),
          items:
              items.map((String item) {
                return DropdownMenuItem<String>(
                  value: item,
                  child: Text(item, style: const TextStyle(fontFamily: 'LCB')),
                );
              }).toList(),
        ),
      ],
    );
  }

  Widget _buildActionSetting({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade200),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFF2C3E50).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: const Color(0xFF2C3E50), size: 20),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF2C3E50),
                      fontFamily: 'LCB',
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                      fontFamily: 'LCB',
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colors.grey.shade600,
            ),
          ],
        ),
      ),
    );
  }

  // Action methods
  void _pickImage() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
      );

      if (result != null && result.files.single.bytes != null) {
        final bytes = result.files.single.bytes!;
        final fileName = result.files.single.name;

        // Convert bytes to base64 for web storage
        final base64String = 'data:image/png;base64,${base64Encode(bytes)}';

        setState(() {
          _selectedImagePath = fileName;
        });

        // Update immediately in UI
        if (mounted) {
          final appLogoProvider = Provider.of<AppLogoProvider>(
            context,
            listen: false,
          );
          appLogoProvider.updateAppLogo(base64String);
        }

        if (_currentSettings != null) {
          await _updateSettings(
            _currentSettings!.copyWith(
              appLogo: base64String,
              updatedDate: DateTime.now(),
            ),
          );
        }

        _showSuccessSnackBar('تم اختيار الصورة بنجاح');
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في اختيار الصورة: $e');
    }
  }

  void _createBackup() async {
    try {
      _showLoadingDialog('جاري إنشاء النسخة الاحتياطية...');

      await BackupManager.downloadBackup();

      if (mounted) {
        Navigator.pop(context); // Close loading dialog
        _showSuccessSnackBar('تم إنشاء النسخة الاحتياطية بنجاح');
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // Close loading dialog
        _showErrorSnackBar('فشل في إنشاء النسخة الاحتياطية: $e');
      }
    }
  }

  void _restoreBackup() async {
    try {
      final confirmed = await _showConfirmDialog(
        'استعادة النسخة الاحتياطية',
        'هل أنت متأكد من استعادة النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.',
      );

      if (confirmed) {
        _showLoadingDialog('جاري استعادة النسخة الاحتياطية...');

        await BackupManager.restoreBackup();

        if (mounted) {
          Navigator.pop(context); // Close loading dialog
          _showSuccessSnackBar('تم استعادة النسخة الاحتياطية بنجاح');

          // Reload settings
          await _loadSettings();
        }
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // Close loading dialog
        _showErrorSnackBar('فشل في استعادة النسخة الاحتياطية: $e');
      }
    }
  }

  void _showSupport() {
    _showInfoDialog(
      'الدعم الفني',
      'للتواصل مع الدعم الفني:\nالبريد الإلكتروني: <EMAIL>\nالهاتف: +966123456789',
    );
  }

  void _showAbout() {
    _showInfoDialog(
      'حول البرنامج',
      'نظام إدارة الديون\nالإصدار: 1.0.0\nالمطور: فريق التطوير\nحقوق الطبع محفوظة © 2024',
    );
  }

  void _rateApp() {
    _showInfoDialog(
      'تقييم التطبيق',
      'شكراً لك! سيتم توجيهك لمتجر التطبيقات لتقييم البرنامج',
    );
  }

  void _showLoadingDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            content: Row(
              children: [
                const CircularProgressIndicator(),
                const SizedBox(width: 20),
                Expanded(
                  child: Text(
                    message,
                    style: const TextStyle(fontFamily: 'LCB'),
                  ),
                ),
              ],
            ),
          ),
    );
  }

  Future<bool> _showConfirmDialog(String title, String content) async {
    final result = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(title, style: const TextStyle(fontFamily: 'LCB')),
            content: Text(content, style: const TextStyle(fontFamily: 'LCB')),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('إلغاء', style: TextStyle(fontFamily: 'LCB')),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: const Text('موافق', style: TextStyle(fontFamily: 'LCB')),
              ),
            ],
          ),
    );
    return result ?? false;
  }

  void _showInfoDialog(String title, String content) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(title, style: const TextStyle(fontFamily: 'LCB')),
            content: Text(content, style: const TextStyle(fontFamily: 'LCB')),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق', style: TextStyle(fontFamily: 'LCB')),
              ),
            ],
          ),
    );
  }

  @override
  void dispose() {
    _appNameController.dispose();
    super.dispose();
  }
}
