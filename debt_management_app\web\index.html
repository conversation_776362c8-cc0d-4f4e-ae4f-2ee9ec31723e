<!DOCTYPE html>
<html>
<head>
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <meta name="description" content="نظام إدارة الديون - تطبيق احترافي لإدارة الديون والمدفوعات">
  <meta name="keywords" content="إدارة الديون, نظام محاسبي, تطبيق ديون, إدارة مالية">
  <meta name="author" content="نظام إدارة الديون">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <meta name="apple-mobile-web-app-title" content="إدارة الديون">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>نظام إدارة الديون</title>
  <link rel="manifest" href="manifest.json">

  <!-- Responsive Design -->
  <style>
    body {
      margin: 0;
      padding: 0;
      background-color: #F5F6FA;
      font-family: 'LCB', Arial, sans-serif;
      overflow-x: hidden;
    }

    #loading {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #2C3E50, #34495E);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9999;
    }

    .loader {
      width: 60px;
      height: 60px;
      border: 4px solid rgba(255, 255, 255, 0.3);
      border-top: 4px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .loading-text {
      color: white;
      margin-top: 20px;
      font-size: 18px;
      font-weight: 600;
    }
  </style>
</head>
<body>
  <!-- Loading Screen -->
  <div id="loading">
    <div style="text-align: center;">
      <div class="loader"></div>
      <div class="loading-text">جاري تحميل نظام إدارة الديون...</div>
    </div>
  </div>

  <script>
    // Hide loading screen when Flutter app loads
    window.addEventListener('flutter-first-frame', function () {
      const loading = document.getElementById('loading');
      if (loading) {
        loading.style.opacity = '0';
        setTimeout(() => loading.remove(), 300);
      }
    });

    // Disable DevTools in production to avoid errors
    if (window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {
      window.flutterConfiguration = {
        canvasKitBaseUrl: "https://www.gstatic.com/flutter-canvaskit/",
        debugShowCheckedModeBanner: false,
      };
    }
  </script>

  <script src="flutter_bootstrap.js" async></script>
</body>
</html>
