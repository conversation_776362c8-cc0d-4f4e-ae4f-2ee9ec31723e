import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'dart:async';

class DateTimeDisplay extends StatefulWidget {
  const DateTimeDisplay({super.key});

  @override
  State<DateTimeDisplay> createState() => _DateTimeDisplayState();
}

class _DateTimeDisplayState extends State<DateTimeDisplay> {
  late Timer _timer;
  DateTime _currentTime = DateTime.now();

  @override
  void initState() {
    super.initState();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _currentTime = DateTime.now();
      });
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        bool isSmallScreen = constraints.maxWidth < 600;
        bool isMediumScreen = constraints.maxWidth < 1000;

        double timeSize = isSmallScreen ? 36 : (isMediumScreen ? 44 : 52);
        double dateSize = isSmallScreen ? 16 : (isMediumScreen ? 18 : 20);
        double daySize = isSmallScreen ? 14 : 16;

        return Container(
          margin: EdgeInsets.symmetric(vertical: isSmallScreen ? 16 : 24),
          padding: EdgeInsets.symmetric(
            horizontal: isSmallScreen ? 16 : 20,
            vertical: isSmallScreen ? 8 : 12,
          ),
          child: Column(
            children: [
              // Time Display
              Text(
                _formatTime(_currentTime),
                style: TextStyle(
                  fontSize: timeSize,
                  fontWeight: FontWeight.w300,
                  color: const Color(0xFF2C3E50),
                  fontFamily: 'LCB',
                  letterSpacing: isSmallScreen ? 2 : 3,
                ),
              ),
              SizedBox(height: isSmallScreen ? 8 : 12),

              // Date Display
              Text(
                _formatDate(_currentTime),
                style: TextStyle(
                  fontSize: dateSize,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey.shade700,
                  fontFamily: 'LCB',
                  letterSpacing: 1,
                ),
              ),

              SizedBox(height: isSmallScreen ? 4 : 6),

              // Day of Week
              Text(
                _formatDayOfWeek(_currentTime),
                style: TextStyle(
                  fontSize: daySize,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey.shade500,
                  fontFamily: 'LCB',
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  String _formatTime(DateTime dateTime) {
    return DateFormat('HH:mm:ss').format(dateTime);
  }

  String _formatDate(DateTime dateTime) {
    // Arabic date format
    final months = [
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر',
    ];

    return '${dateTime.day} ${months[dateTime.month - 1]} ${dateTime.year}';
  }

  String _formatDayOfWeek(DateTime dateTime) {
    final days = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد',
    ];

    return days[dateTime.weekday - 1];
  }
}
