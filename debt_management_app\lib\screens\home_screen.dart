import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/custom_app_bar.dart';
import '../utils/settings_manager.dart';
import '../widgets/date_time_display.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();

    // تحديث حالة التنقل عند دخول الصفحة الرئيسية
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        final navigationProvider = Provider.of<NavigationProvider>(
          context,
          listen: false,
        );
        navigationProvider.goToHome();
      }
    });
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
      ),
    );

    _scaleAnimation = Tween<double>(begin: 0.9, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.2, 1.0, curve: Curves.elasticOut),
      ),
    );

    // Start animation after frame is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) _animationController.forward();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F6FA),
      appBar: const CustomAppBar(),
      body: LayoutBuilder(
        builder: (context, constraints) {
          bool isSmallScreen = constraints.maxWidth < 600;
          bool isMediumScreen = constraints.maxWidth < 1000;

          return AnimatedBuilder(
            animation: _fadeAnimation,
            builder: (context, child) {
              return Opacity(
                opacity: _fadeAnimation.value,
                child: SingleChildScrollView(
                  padding: EdgeInsets.all(isSmallScreen ? 16.0 : 20.0),
                  child: Column(
                    children: [
                      // Date and Time Display
                      const Center(child: DateTimeDisplay()),

                      SizedBox(height: isSmallScreen ? 40 : 60),

                      // App Logo in Center
                      _buildCenterLogo(isSmallScreen),

                      SizedBox(height: isSmallScreen ? 20 : 30),

                      // App Title
                      _buildAppTitle(isSmallScreen, isMediumScreen),

                      SizedBox(height: isSmallScreen ? 60 : 100),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildCenterLogo(bool isSmallScreen) {
    double logoSize = isSmallScreen ? 100 : 120;
    double iconSize = isSmallScreen ? 50 : 60;

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            width: logoSize,
            height: logoSize,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [const Color(0xFF2C3E50), const Color(0xFF34495E)],
              ),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF2C3E50).withValues(alpha: 0.3),
                  blurRadius: isSmallScreen ? 15 : 20,
                  offset: Offset(0, isSmallScreen ? 6 : 8),
                ),
              ],
            ),
            child: Icon(
              Icons.account_balance,
              color: Colors.white,
              size: iconSize,
            ),
          ),
        );
      },
    );
  }

  Widget _buildAppTitle(bool isSmallScreen, bool isMediumScreen) {
    double fontSize = isSmallScreen ? 22 : (isMediumScreen ? 25 : 28);

    return Text(
      'نظام إدارة الديون',
      textAlign: TextAlign.center,
      style: TextStyle(
        fontSize: fontSize,
        fontWeight: FontWeight.bold,
        color: const Color(0xFF2C3E50),
        fontFamily: 'LCB',
        letterSpacing: isSmallScreen ? 1.0 : 1.5,
      ),
    );
  }
}
