import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/debt_model.dart';
import '../models/client_model.dart';
import '../models/user_model.dart';
import '../models/payment_model.dart';
import '../models/settings_model.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  factory DatabaseHelper() => _instance;
  DatabaseHelper._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'debt_management.db');
    return await openDatabase(path, version: 1, onCreate: _onCreate);
  }

  Future<void> _onCreate(Database db, int version) async {
    // Create users table
    await db.execute('''
      CREATE TABLE users(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        email TEXT NOT NULL UNIQUE,
        phone TEXT,
        profile_image TEXT,
        created_date INTEGER NOT NULL,
        is_active INTEGER NOT NULL DEFAULT 1
      )
    ''');

    // Create clients table
    await db.execute('''
      CREATE TABLE clients(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        phone TEXT NOT NULL,
        email TEXT,
        address TEXT,
        notes TEXT,
        created_date INTEGER NOT NULL
      )
    ''');

    // Create debts table
    await db.execute('''
      CREATE TABLE debts(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        client_id INTEGER NOT NULL,
        amount REAL NOT NULL,
        paid_amount REAL NOT NULL DEFAULT 0.0,
        description TEXT NOT NULL,
        created_date INTEGER NOT NULL,
        due_date INTEGER,
        status TEXT NOT NULL DEFAULT 'active',
        type TEXT NOT NULL,
        FOREIGN KEY (client_id) REFERENCES clients (id)
      )
    ''');

    // Create payments table
    await db.execute('''
      CREATE TABLE payments(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        debt_id INTEGER NOT NULL,
        amount REAL NOT NULL,
        payment_date INTEGER NOT NULL,
        notes TEXT,
        payment_method TEXT NOT NULL DEFAULT 'cash',
        FOREIGN KEY (debt_id) REFERENCES debts (id)
      )
    ''');

    // Create settings table
    await db.execute('''
      CREATE TABLE settings(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        app_name TEXT NOT NULL DEFAULT 'إدارة الديون',
        app_logo TEXT,
        is_dark_mode INTEGER NOT NULL DEFAULT 0,
        language TEXT NOT NULL DEFAULT 'ar',
        currency TEXT NOT NULL DEFAULT 'ر.س',
        enable_notifications INTEGER NOT NULL DEFAULT 1,
        enable_backup INTEGER NOT NULL DEFAULT 1,
        last_backup_date TEXT,
        updated_date INTEGER NOT NULL
      )
    ''');

    // Insert default settings
    await db.insert('settings', {
      'app_name': 'إدارة الديون',
      'is_dark_mode': 0,
      'language': 'ar',
      'currency': 'ر.س',
      'enable_notifications': 1,
      'enable_backup': 1,
      'updated_date': DateTime.now().millisecondsSinceEpoch,
    });
  }

  // User operations
  Future<int> insertUser(User user) async {
    final db = await database;
    return await db.insert('users', user.toMap());
  }

  Future<List<User>> getUsers() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('users');
    return List.generate(maps.length, (i) => User.fromMap(maps[i]));
  }

  Future<User?> getUser(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'users',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return User.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateUser(User user) async {
    final db = await database;
    return await db.update(
      'users',
      user.toMap(),
      where: 'id = ?',
      whereArgs: [user.id],
    );
  }

  Future<int> deleteUser(int id) async {
    final db = await database;
    return await db.delete('users', where: 'id = ?', whereArgs: [id]);
  }

  // Client operations
  Future<int> insertClient(Client client) async {
    final db = await database;
    return await db.insert('clients', client.toMap());
  }

  Future<List<Client>> getClients() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('clients');
    return List.generate(maps.length, (i) => Client.fromMap(maps[i]));
  }

  Future<Client?> getClient(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'clients',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Client.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateClient(Client client) async {
    final db = await database;
    return await db.update(
      'clients',
      client.toMap(),
      where: 'id = ?',
      whereArgs: [client.id],
    );
  }

  Future<int> deleteClient(int id) async {
    final db = await database;
    return await db.delete('clients', where: 'id = ?', whereArgs: [id]);
  }

  // Debt operations
  Future<int> insertDebt(Debt debt) async {
    final db = await database;
    return await db.insert('debts', debt.toMap());
  }

  Future<List<Debt>> getDebts() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('debts');
    return List.generate(maps.length, (i) => Debt.fromMap(maps[i]));
  }

  Future<Debt?> getDebt(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'debts',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Debt.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateDebt(Debt debt) async {
    final db = await database;
    return await db.update(
      'debts',
      debt.toMap(),
      where: 'id = ?',
      whereArgs: [debt.id],
    );
  }

  Future<int> deleteDebt(int id) async {
    final db = await database;
    return await db.delete('debts', where: 'id = ?', whereArgs: [id]);
  }

  // Payment operations
  Future<int> insertPayment(Payment payment) async {
    final db = await database;
    return await db.insert('payments', payment.toMap());
  }

  Future<List<Payment>> getPayments() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('payments');
    return List.generate(maps.length, (i) => Payment.fromMap(maps[i]));
  }

  Future<List<Payment>> getPaymentsByDebt(int debtId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'payments',
      where: 'debt_id = ?',
      whereArgs: [debtId],
    );
    return List.generate(maps.length, (i) => Payment.fromMap(maps[i]));
  }

  Future<int> updatePayment(Payment payment) async {
    final db = await database;
    return await db.update(
      'payments',
      payment.toMap(),
      where: 'id = ?',
      whereArgs: [payment.id],
    );
  }

  Future<int> deletePayment(int id) async {
    final db = await database;
    return await db.delete('payments', where: 'id = ?', whereArgs: [id]);
  }

  // Statistics and reports
  Future<Map<String, double>> getDebtStatistics() async {
    final db = await database;

    final totalDebtsResult = await db.rawQuery(
      'SELECT SUM(amount) as total FROM debts WHERE type = "debt_to_me"',
    );
    final totalCreditsResult = await db.rawQuery(
      'SELECT SUM(amount) as total FROM debts WHERE type = "debt_from_me"',
    );
    final totalPaidResult = await db.rawQuery(
      'SELECT SUM(paid_amount) as total FROM debts',
    );

    double totalDebts = totalDebtsResult.first['total'] as double? ?? 0.0;
    double totalCredits = totalCreditsResult.first['total'] as double? ?? 0.0;
    double totalPaid = totalPaidResult.first['total'] as double? ?? 0.0;

    return {
      'totalDebts': totalDebts,
      'totalCredits': totalCredits,
      'totalPaid': totalPaid,
      'netBalance': totalDebts - totalCredits,
    };
  }

  // Settings operations
  Future<AppSettings> getSettings() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'settings',
      limit: 1,
    );
    if (maps.isNotEmpty) {
      return AppSettings.fromMap(maps.first);
    }
    // Return default settings if none exist
    return AppSettings(updatedDate: DateTime.now());
  }

  Future<int> updateSettings(AppSettings settings) async {
    final db = await database;
    final existingSettings = await db.query('settings', limit: 1);

    if (existingSettings.isNotEmpty) {
      return await db.update(
        'settings',
        settings.toMap(),
        where: 'id = ?',
        whereArgs: [existingSettings.first['id']],
      );
    } else {
      return await db.insert('settings', settings.toMap());
    }
  }

  Future<void> close() async {
    final db = await database;
    db.close();
  }
}
